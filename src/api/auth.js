import request from '@/utils/request'
import { users } from '@/mock/data'

// 模拟API延迟
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))

// 登录
export const login = async (credentials) => {
  await delay(500) // 模拟网络延迟
  
  const { username, password } = credentials
  const user = users.find(u => u.username === username && u.password === password)
  
  if (user) {
    const { password: _, ...userInfo } = user
    return {
      code: 200,
      data: {
        user: userInfo,
        token: `mock_token_${user.id}_${Date.now()}`
      },
      message: '登录成功'
    }
  } else {
    throw new Error('用户名或密码错误')
  }
}

// 注册
export const register = async (userInfo) => {
  await delay(500)
  
  const { username, email, password } = userInfo
  
  // 检查用户名是否已存在
  const existingUser = users.find(u => u.username === username || u.email === email)
  if (existingUser) {
    throw new Error('用户名或邮箱已存在')
  }
  
  // 创建新用户
  const newUser = {
    id: users.length + 1,
    username,
    email,
    password,
    role: 'user',
    balance: 100, // 新用户赠送100元
    avatar: `https://via.placeholder.com/100x100?text=${username.charAt(0).toUpperCase()}`,
    createdAt: new Date().toISOString().split('T')[0]
  }
  
  users.push(newUser)
  
  const { password: _, ...userResponse } = newUser
  return {
    code: 200,
    data: {
      user: userResponse,
      token: `mock_token_${newUser.id}_${Date.now()}`
    },
    message: '注册成功'
  }
}

// 获取用户信息
export const getUserInfo = async () => {
  await delay(300)
  
  // 这里应该根据token获取用户信息，mock中简化处理
  return {
    code: 200,
    data: users[1], // 返回默认用户
    message: '获取成功'
  }
}

// 更新用户信息
export const updateUserInfo = async (userInfo) => {
  await delay(500)
  
  return {
    code: 200,
    data: userInfo,
    message: '更新成功'
  }
}
