import request from '@/utils/request'
import { orders, books, users } from '@/mock/data'

// 模拟API延迟
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))

// 创建订单（购买图书）
export const createOrder = async (orderData) => {
  await delay(500)
  
  const { bookId, buyerId } = orderData
  
  // 检查图书是否存在且可购买
  const book = books.find(b => b.id === bookId)
  if (!book) {
    throw new Error('图书不存在')
  }
  
  if (book.status !== 'available' || book.stock <= 0) {
    throw new Error('图书已售出或不可购买')
  }
  
  // 检查买家余额
  const buyer = users.find(u => u.id === buyerId)
  if (!buyer) {
    throw new Error('用户不存在')
  }
  
  if (buyer.balance < book.price) {
    throw new Error('余额不足')
  }
  
  // 检查是否为自己发布的图书
  if (book.sellerId === buyerId) {
    throw new Error('不能购买自己发布的图书')
  }
  
  // 创建订单
  const newOrder = {
    id: orders.length + 1,
    bookId: book.id,
    bookTitle: book.title,
    bookCover: book.cover,
    buyerId,
    buyerName: buyer.username,
    sellerId: book.sellerId,
    sellerName: book.sellerName,
    price: book.price,
    status: 'completed',
    createdAt: new Date().toISOString().split('T')[0],
    completedAt: new Date().toISOString().split('T')[0]
  }
  
  orders.push(newOrder)
  
  // 更新图书状态
  book.status = 'sold'
  book.stock = 0
  book.soldAt = new Date().toISOString().split('T')[0]
  
  // 更新用户余额
  buyer.balance -= book.price
  const seller = users.find(u => u.id === book.sellerId)
  if (seller) {
    seller.balance += book.price
  }

  return newOrder
}

// 获取订单列表
export const getOrders = async (params = {}) => {
  await delay(300)
  
  let filteredOrders = [...orders]
  
  // 筛选条件
  if (params.buyerId) {
    filteredOrders = filteredOrders.filter(order => order.buyerId === params.buyerId)
  }
  
  if (params.sellerId) {
    filteredOrders = filteredOrders.filter(order => order.sellerId === params.sellerId)
  }
  
  if (params.status) {
    filteredOrders = filteredOrders.filter(order => order.status === params.status)
  }
  
  // 排序（按创建时间倒序）
  filteredOrders.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
  
  // 分页
  const page = params.page || 1
  const pageSize = params.pageSize || 10
  const start = (page - 1) * pageSize
  const end = start + pageSize
  
  return {
    list: filteredOrders.slice(start, end),
    total: filteredOrders.length,
    page,
    pageSize
  }
}

// 获取订单详情
export const getOrderDetail = async (id) => {
  await delay(200)
  
  const order = orders.find(o => o.id === parseInt(id))
  if (!order) {
    throw new Error('订单不存在')
  }
  
  return order
}
