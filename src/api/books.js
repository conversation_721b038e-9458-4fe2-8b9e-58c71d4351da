import request from '@/utils/request'
import { books, categories } from '@/mock/data'

// 模拟API延迟
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))

// 获取图书列表
export const getBooks = async (params = {}) => {
  await delay(300)
  
  let filteredBooks = [...books]
  
  // 筛选条件
  if (params.category && params.category !== 'all') {
    filteredBooks = filteredBooks.filter(book => book.category === params.category)
  }
  
  if (params.status) {
    filteredBooks = filteredBooks.filter(book => book.status === params.status)
  }
  
  if (params.sellerId) {
    filteredBooks = filteredBooks.filter(book => book.sellerId === params.sellerId)
  }
  
  if (params.keyword) {
    const keyword = params.keyword.toLowerCase()
    filteredBooks = filteredBooks.filter(book => 
      book.title.toLowerCase().includes(keyword) ||
      book.author.toLowerCase().includes(keyword) ||
      book.description.toLowerCase().includes(keyword)
    )
  }
  
  // 排序
  if (params.sortBy) {
    switch (params.sortBy) {
      case 'price_asc':
        filteredBooks.sort((a, b) => a.price - b.price)
        break
      case 'price_desc':
        filteredBooks.sort((a, b) => b.price - a.price)
        break
      case 'time_desc':
        filteredBooks.sort((a, b) => new Date(b.publishedAt) - new Date(a.publishedAt))
        break
      case 'views_desc':
        filteredBooks.sort((a, b) => b.views - a.views)
        break
    }
  }
  
  // 分页
  const page = params.page || 1
  const pageSize = params.pageSize || 12
  const start = (page - 1) * pageSize
  const end = start + pageSize
  
  return {
    list: filteredBooks.slice(start, end),
    total: filteredBooks.length,
    page,
    pageSize
  }
}

// 获取图书详情
export const getBookDetail = async (id) => {
  await delay(200)
  
  const book = books.find(b => b.id === parseInt(id))
  if (!book) {
    throw new Error('图书不存在')
  }
  
  // 增加浏览量
  book.views += 1

  return book
}

// 发布图书
export const publishBook = async (bookData) => {
  await delay(500)
  
  const newBook = {
    id: books.length + 1,
    ...bookData,
    status: 'available',
    stock: 1,
    publishedAt: new Date().toISOString().split('T')[0],
    views: 0
  }
  
  books.push(newBook)

  return newBook
}

// 更新图书信息
export const updateBook = async (id, bookData) => {
  await delay(500)
  
  const bookIndex = books.findIndex(b => b.id === parseInt(id))
  if (bookIndex === -1) {
    throw new Error('图书不存在')
  }
  
  books[bookIndex] = { ...books[bookIndex], ...bookData }

  return books[bookIndex]
}

// 删除图书
export const deleteBook = async (id) => {
  await delay(300)
  
  const bookIndex = books.findIndex(b => b.id === parseInt(id))
  if (bookIndex === -1) {
    throw new Error('图书不存在')
  }
  
  books.splice(bookIndex, 1)

  return true
}

// 获取分类列表
export const getCategories = async () => {
  await delay(200)
  
  return categories
}
