import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { createOrder, getOrders, getOrderDetail } from '@/api/orders'

export const useOrdersStore = defineStore('orders', () => {
  // 状态
  const orders = ref([])
  const currentOrder = ref(null)
  const loading = ref(false)
  const total = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(10)

  // 计算属性
  const completedOrders = computed(() => 
    orders.value.filter(order => order.status === 'completed')
  )

  const pendingOrders = computed(() => 
    orders.value.filter(order => order.status === 'pending')
  )

  // 创建订单（购买图书）
  const buyBook = async (bookId, buyerId) => {
    loading.value = true
    try {
      const response = await createOrder({ bookId, buyerId })
      
      // 添加到订单列表开头
      orders.value.unshift(response)
      total.value += 1
      
      return response
    } catch (error) {
      console.error('购买图书失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取订单列表
  const fetchOrders = async (params = {}) => {
    loading.value = true
    try {
      const response = await getOrders({
        page: currentPage.value,
        pageSize: pageSize.value,
        ...params
      })
      
      orders.value = response.list
      total.value = response.total
      currentPage.value = response.page
      
      return response
    } catch (error) {
      console.error('获取订单列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取订单详情
  const fetchOrderDetail = async (id) => {
    loading.value = true
    try {
      const response = await getOrderDetail(id)
      currentOrder.value = response
      return response
    } catch (error) {
      console.error('获取订单详情失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取用户购买的订单
  const getBuyOrders = async (buyerId) => {
    return await fetchOrders({ buyerId })
  }

  // 获取用户销售的订单
  const getSellOrders = async (sellerId) => {
    return await fetchOrders({ sellerId })
  }

  // 重置状态
  const resetState = () => {
    orders.value = []
    currentOrder.value = null
    total.value = 0
    currentPage.value = 1
    loading.value = false
  }

  // 设置页码
  const setPage = (page) => {
    currentPage.value = page
  }

  return {
    // 状态
    orders,
    currentOrder,
    loading,
    total,
    currentPage,
    pageSize,
    
    // 计算属性
    completedOrders,
    pendingOrders,
    
    // 方法
    buyBook,
    fetchOrders,
    fetchOrderDetail,
    getBuyOrders,
    getSellOrders,
    resetState,
    setPage
  }
})
