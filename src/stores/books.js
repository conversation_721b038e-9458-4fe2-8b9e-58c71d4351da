import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getBooks, getBookDetail, publishBook, updateBook, deleteBook, getCategories } from '@/api/books'

export const useBooksStore = defineStore('books', () => {
  // 状态
  const books = ref([])
  const currentBook = ref(null)
  const categories = ref([])
  const loading = ref(false)
  const total = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(12)

  // 计算属性
  const availableBooks = computed(() => 
    books.value.filter(book => book.status === 'available')
  )

  const soldBooks = computed(() => 
    books.value.filter(book => book.status === 'sold')
  )

  // 获取图书列表
  const fetchBooks = async (params = {}) => {
    loading.value = true
    try {
      const response = await getBooks({
        page: currentPage.value,
        pageSize: pageSize.value,
        ...params
      })
      
      books.value = response.list
      total.value = response.total
      currentPage.value = response.page
      
      return response
    } catch (error) {
      console.error('获取图书列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取图书详情
  const fetchBookDetail = async (id) => {
    loading.value = true
    try {
      const response = await getBookDetail(id)
      currentBook.value = response
      return response
    } catch (error) {
      console.error('获取图书详情失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 发布图书
  const createBook = async (bookData) => {
    loading.value = true
    try {
      const response = await publishBook(bookData)
      // 添加到列表开头
      books.value.unshift(response)
      total.value += 1
      return response
    } catch (error) {
      console.error('发布图书失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新图书
  const editBook = async (id, bookData) => {
    loading.value = true
    try {
      const response = await updateBook(id, bookData)
      
      // 更新列表中的图书
      const index = books.value.findIndex(book => book.id === id)
      if (index !== -1) {
        books.value[index] = response
      }
      
      // 更新当前图书
      if (currentBook.value && currentBook.value.id === id) {
        currentBook.value = response
      }
      
      return response
    } catch (error) {
      console.error('更新图书失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 删除图书
  const removeBook = async (id) => {
    loading.value = true
    try {
      await deleteBook(id)
      
      // 从列表中移除
      const index = books.value.findIndex(book => book.id === id)
      if (index !== -1) {
        books.value.splice(index, 1)
        total.value -= 1
      }
      
      // 清除当前图书
      if (currentBook.value && currentBook.value.id === id) {
        currentBook.value = null
      }
      
      return true
    } catch (error) {
      console.error('删除图书失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      const response = await getCategories()
      categories.value = response
      return response
    } catch (error) {
      console.error('获取分类列表失败:', error)
      throw error
    }
  }

  // 搜索图书
  const searchBooks = async (keyword, filters = {}) => {
    return await fetchBooks({
      keyword,
      ...filters
    })
  }

  // 按分类获取图书
  const getBooksByCategory = async (category) => {
    return await fetchBooks({ category })
  }

  // 获取用户发布的图书
  const getUserBooks = async (sellerId) => {
    return await fetchBooks({ sellerId })
  }

  // 重置状态
  const resetState = () => {
    books.value = []
    currentBook.value = null
    total.value = 0
    currentPage.value = 1
    loading.value = false
  }

  // 设置页码
  const setPage = (page) => {
    currentPage.value = page
  }

  return {
    // 状态
    books,
    currentBook,
    categories,
    loading,
    total,
    currentPage,
    pageSize,
    
    // 计算属性
    availableBooks,
    soldBooks,
    
    // 方法
    fetchBooks,
    fetchBookDetail,
    createBook,
    editBook,
    removeBook,
    fetchCategories,
    searchBooks,
    getBooksByCategory,
    getUserBooks,
    resetState,
    setPage
  }
})
