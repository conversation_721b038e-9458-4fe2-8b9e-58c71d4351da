import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login, register, getUserInfo } from '@/api/auth'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref(null)
  const token = ref(localStorage.getItem('token') || '')
  const isLoggedIn = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')

  // 登录
  const loginUser = async (credentials) => {
    try {
      const response = await login(credentials)
      user.value = response.user
      token.value = response.token
      
      // 保存到本地存储
      localStorage.setItem('token', response.token)
      localStorage.setItem('user', JSON.stringify(response.user))
      
      return response
    } catch (error) {
      throw error
    }
  }

  // 注册
  const registerUser = async (userInfo) => {
    try {
      const response = await register(userInfo)
      user.value = response.user
      token.value = response.token
      
      // 保存到本地存储
      localStorage.setItem('token', response.token)
      localStorage.setItem('user', JSON.stringify(response.user))
      
      return response
    } catch (error) {
      throw error
    }
  }

  // 登出
  const logout = () => {
    user.value = null
    token.value = ''
    
    // 清除本地存储
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  }

  // 更新用户信息
  const updateUser = (userInfo) => {
    user.value = { ...user.value, ...userInfo }
    localStorage.setItem('user', JSON.stringify(user.value))
  }

  // 更新余额
  const updateBalance = (amount) => {
    if (user.value) {
      user.value.balance = amount
      localStorage.setItem('user', JSON.stringify(user.value))
    }
  }

  // 初始化用户信息（从本地存储恢复）
  const initUser = () => {
    const savedUser = localStorage.getItem('user')
    if (savedUser) {
      try {
        user.value = JSON.parse(savedUser)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        logout()
      }
    }
  }

  // 获取最新用户信息
  const fetchUserInfo = async () => {
    try {
      const response = await getUserInfo()
      user.value = response
      localStorage.setItem('user', JSON.stringify(response))
      return response
    } catch (error) {
      console.error('获取用户信息失败:', error)
      logout()
      throw error
    }
  }

  return {
    // 状态
    user,
    token,
    isLoggedIn,
    isAdmin,
    
    // 方法
    loginUser,
    registerUser,
    logout,
    updateUser,
    updateBalance,
    initUser,
    fetchUserInfo
  }
})
