// Mock 数据

// 用户数据
export const users = [
  {
    id: 1,
    username: 'admin',
    password: '123456',
    email: '<EMAIL>',
    role: 'admin',
    balance: 10000,
    avatar: 'https://via.placeholder.com/100x100?text=Admin',
    createdAt: '2024-01-01'
  },
  {
    id: 2,
    username: 'student1',
    password: '123456',
    email: '<EMAIL>',
    role: 'user',
    balance: 500,
    avatar: 'https://via.placeholder.com/100x100?text=S1',
    createdAt: '2024-01-02'
  },
  {
    id: 3,
    username: 'student2',
    password: '123456',
    email: '<EMAIL>',
    role: 'user',
    balance: 300,
    avatar: 'https://via.placeholder.com/100x100?text=S2',
    createdAt: '2024-01-03'
  }
]

// 图书数据
export const books = [
  {
    id: 1,
    title: '高等数学（上册）',
    author: '同济大学数学系',
    description: '经典的高等数学教材，适合理工科学生使用。书籍保存完好，无破损。',
    price: 25.00,
    originalPrice: 45.00,
    cover: 'https://via.placeholder.com/200x280?text=高等数学',
    category: '数学',
    condition: '九成新',
    sellerId: 2,
    sellerName: 'student1',
    status: 'available', // available, sold, offline
    stock: 1,
    publishedAt: '2024-01-10',
    views: 156
  },
  {
    id: 2,
    title: '大学英语综合教程1',
    author: '李荫华',
    description: '大学英语必修教材，内容丰富，适合英语学习。',
    price: 18.00,
    originalPrice: 32.00,
    cover: 'https://via.placeholder.com/200x280?text=大学英语',
    category: '英语',
    condition: '八成新',
    sellerId: 3,
    sellerName: 'student2',
    status: 'available',
    stock: 1,
    publishedAt: '2024-01-12',
    views: 89
  },
  {
    id: 3,
    title: 'Java程序设计教程',
    author: '谭浩强',
    description: 'Java编程入门经典教材，代码示例丰富，适合初学者。',
    price: 35.00,
    originalPrice: 58.00,
    cover: 'https://via.placeholder.com/200x280?text=Java教程',
    category: '计算机',
    condition: '九成新',
    sellerId: 2,
    sellerName: 'student1',
    status: 'available',
    stock: 1,
    publishedAt: '2024-01-15',
    views: 234
  },
  {
    id: 4,
    title: '线性代数',
    author: '居余马',
    description: '线性代数基础教材，理论清晰，例题丰富。',
    price: 22.00,
    originalPrice: 38.00,
    cover: 'https://via.placeholder.com/200x280?text=线性代数',
    category: '数学',
    condition: '八成新',
    sellerId: 3,
    sellerName: 'student2',
    status: 'sold',
    stock: 0,
    publishedAt: '2024-01-08',
    views: 178,
    soldAt: '2024-01-20'
  }
]

// 订单数据
export const orders = [
  {
    id: 1,
    bookId: 4,
    bookTitle: '线性代数',
    bookCover: 'https://via.placeholder.com/200x280?text=线性代数',
    buyerId: 2,
    buyerName: 'student1',
    sellerId: 3,
    sellerName: 'student2',
    price: 22.00,
    status: 'completed', // pending, completed, cancelled
    createdAt: '2024-01-20',
    completedAt: '2024-01-20'
  }
]

// 分类数据
export const categories = [
  { id: 1, name: '数学', count: 15 },
  { id: 2, name: '英语', count: 8 },
  { id: 3, name: '计算机', count: 12 },
  { id: 4, name: '物理', count: 6 },
  { id: 5, name: '化学', count: 4 },
  { id: 6, name: '文学', count: 9 },
  { id: 7, name: '历史', count: 3 },
  { id: 8, name: '其他', count: 7 }
]
