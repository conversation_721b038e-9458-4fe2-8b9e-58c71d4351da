<template>
  <div>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
      <div class="container">
        <router-link class="navbar-brand" to="/">
          <i class="bi bi-book me-2"></i>
          校园二手书交易系统
        </router-link>
        
        <div class="navbar-nav ms-auto">
          <router-link class="nav-link" to="/">
            <i class="bi bi-arrow-left me-1"></i>返回首页
          </router-link>
        </div>
      </div>
    </nav>

    <div class="container mt-4">
      <div v-if="booksStore.loading" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">加载中...</span>
        </div>
      </div>

      <div v-else-if="!book" class="text-center py-5">
        <i class="bi bi-exclamation-triangle display-1 text-warning"></i>
        <h4 class="text-muted mt-3">图书不存在</h4>
        <router-link to="/" class="btn btn-primary">返回首页</router-link>
      </div>

      <div v-else class="row">
        <div class="col-md-4">
          <div class="card">
            <img :src="book.cover" class="card-img-top" alt="图书封面">
            <div class="card-body text-center">
              <div class="mb-3">
                <span
                  class="badge fs-6 px-3 py-2"
                  :class="book.status === 'available' ? 'bg-success' : 'bg-secondary'"
                >
                  {{ book.status === 'available' ? '在售中' : '已售出' }}
                </span>
              </div>
              
              <div v-if="book.status === 'available'" class="d-grid gap-2">
                <button
                  class="btn btn-primary btn-lg"
                  @click="handlePurchase"
                  :disabled="!userStore.isLoggedIn || book.sellerId === userStore.user?.id || purchasing"
                >
                  <span v-if="purchasing" class="spinner-border spinner-border-sm me-2"></span>
                  <i v-else class="bi bi-cart-plus me-2"></i>
                  {{ getPurchaseButtonText() }}
                </button>
                
                <div v-if="userStore.isLoggedIn && userStore.user.balance < book.price" class="alert alert-warning small">
                  <i class="bi bi-exclamation-triangle me-1"></i>
                  余额不足，请先充值
                </div>
              </div>
              
              <div v-else class="text-muted">
                <i class="bi bi-check-circle me-1"></i>
                已于 {{ book.soldAt }} 售出
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-8">
          <div class="card">
            <div class="card-body">
              <h2 class="card-title">{{ book.title }}</h2>
              
              <div class="row mb-3">
                <div class="col-sm-6">
                  <strong>作者：</strong>{{ book.author }}
                </div>
                <div class="col-sm-6">
                  <strong>分类：</strong>
                  <span class="badge bg-secondary">{{ book.category }}</span>
                </div>
              </div>

              <div class="row mb-3">
                <div class="col-sm-6">
                  <strong>售价：</strong>
                  <span class="text-danger fs-4 fw-bold">¥{{ book.price }}</span>
                  <small class="text-muted text-decoration-line-through ms-2">原价 ¥{{ book.originalPrice }}</small>
                </div>
                <div class="col-sm-6">
                  <strong>成色：</strong>{{ book.condition }}
                </div>
              </div>

              <div class="row mb-3">
                <div class="col-sm-6">
                  <strong>卖家：</strong>{{ book.sellerName }}
                </div>
                <div class="col-sm-6">
                  <strong>发布时间：</strong>{{ book.publishedAt }}
                </div>
              </div>

              <div class="row mb-4">
                <div class="col-sm-6">
                  <strong>浏览次数：</strong>{{ book.views }}
                </div>
                <div class="col-sm-6">
                  <strong>库存：</strong>{{ book.stock }}
                </div>
              </div>

              <div class="mb-3">
                <strong>图书描述：</strong>
                <p class="mt-2 text-muted">{{ book.description }}</p>
              </div>

              <!-- 节省金额提示 -->
              <div class="alert alert-info">
                <i class="bi bi-piggy-bank me-2"></i>
                <strong>购买此书可节省：¥{{ (book.originalPrice - book.price).toFixed(2) }}</strong>
                <small class="d-block mt-1">
                  相比原价节省了 {{ Math.round((1 - book.price / book.originalPrice) * 100) }}%
                </small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 购买确认模态框 -->
    <div class="modal fade" id="purchaseModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">确认购买</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <div class="text-center mb-3">
              <img :src="book?.cover" alt="图书封面" class="img-thumbnail" style="max-width: 100px;">
            </div>
            <h6>{{ book?.title }}</h6>
            <p class="text-muted">{{ book?.author }}</p>
            <div class="d-flex justify-content-between">
              <span>售价：</span>
              <span class="text-danger fw-bold">¥{{ book?.price }}</span>
            </div>
            <div class="d-flex justify-content-between">
              <span>当前余额：</span>
              <span>¥{{ userStore.user?.balance }}</span>
            </div>
            <hr>
            <div class="d-flex justify-content-between">
              <span>购买后余额：</span>
              <span :class="{ 'text-danger': userStore.user?.balance < book?.price }">
                ¥{{ (userStore.user?.balance - book?.price).toFixed(2) }}
              </span>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button
              type="button"
              class="btn btn-primary"
              @click="confirmPurchase"
              :disabled="purchasing || userStore.user?.balance < book?.price"
            >
              <span v-if="purchasing" class="spinner-border spinner-border-sm me-2"></span>
              确认购买
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useBooksStore } from '@/stores/books'
import { useOrdersStore } from '@/stores/orders'

export default {
  name: 'BookDetail',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const userStore = useUserStore()
    const booksStore = useBooksStore()
    const ordersStore = useOrdersStore()
    
    const purchasing = ref(false)
    
    const book = computed(() => booksStore.currentBook)

    const getPurchaseButtonText = () => {
      if (!userStore.isLoggedIn) {
        return '请先登录'
      }
      if (book.value?.sellerId === userStore.user?.id) {
        return '不能购买自己的图书'
      }
      if (purchasing.value) {
        return '购买中...'
      }
      return `立即购买 ¥${book.value?.price}`
    }

    const handlePurchase = () => {
      if (!userStore.isLoggedIn) {
        router.push('/login')
        return
      }
      
      if (book.value?.sellerId === userStore.user?.id) {
        return
      }
      
      // 显示购买确认模态框
      const modalElement = document.getElementById('purchaseModal')
      if (modalElement) {
        const modal = new window.bootstrap.Modal(modalElement)
        modal.show()
      }
    }

    const confirmPurchase = async () => {
      if (!book.value || !userStore.user) return
      
      purchasing.value = true
      
      try {
        await ordersStore.buyBook(book.value.id, userStore.user.id)
        
        // 更新用户余额
        userStore.updateBalance(userStore.user.balance - book.value.price)
        
        // 更新图书状态
        book.value.status = 'sold'
        book.value.stock = 0
        
        // 关闭模态框
        const modalElement = document.getElementById('purchaseModal')
        if (modalElement) {
          const modal = window.bootstrap.Modal.getInstance(modalElement)
          if (modal) modal.hide()
        }
        
        // 显示成功消息
        alert('购买成功！')
        
        // 跳转到订单页面
        router.push('/orders')
      } catch (error) {
        alert(error.message || '购买失败，请重试')
      } finally {
        purchasing.value = false
      }
    }

    onMounted(async () => {
      userStore.initUser()
      
      const bookId = parseInt(route.params.id)
      if (bookId) {
        try {
          await booksStore.fetchBookDetail(bookId)
        } catch (error) {
          console.error('加载图书详情失败:', error)
        }
      }
    })

    return {
      userStore,
      booksStore,
      book,
      purchasing,
      getPurchaseButtonText,
      handlePurchase,
      confirmPurchase
    }
  }
}
</script>

<style scoped>
.card-img-top {
  height: 300px;
  object-fit: cover;
}

.img-thumbnail {
  max-height: 100px;
  object-fit: cover;
}
</style>
