<template>
  <div>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
      <div class="container">
        <router-link class="navbar-brand" to="/">
          <i class="bi bi-book me-2"></i>
          校园二手书交易系统
        </router-link>
        
        <div class="navbar-nav ms-auto">
          <router-link class="nav-link" to="/">
            <i class="bi bi-arrow-left me-1"></i>返回首页
          </router-link>
        </div>
      </div>
    </nav>

    <div class="container mt-4">
      <div class="row justify-content-center">
        <div class="col-md-8">
          <div class="card">
            <div class="card-header">
              <h4 class="mb-0">
                <i class="bi bi-plus-circle me-2"></i>
                发布二手图书
              </h4>
            </div>
            <div class="card-body">
              <form @submit.prevent="handleSubmit">
                <div class="row">
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label for="title" class="form-label">图书名称 *</label>
                      <input
                        type="text"
                        class="form-control"
                        id="title"
                        v-model="form.title"
                        :class="{ 'is-invalid': errors.title }"
                        placeholder="请输入图书名称"
                        required
                      >
                      <div v-if="errors.title" class="invalid-feedback">
                        {{ errors.title }}
                      </div>
                    </div>
                  </div>
                  
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label for="author" class="form-label">作者 *</label>
                      <input
                        type="text"
                        class="form-control"
                        id="author"
                        v-model="form.author"
                        :class="{ 'is-invalid': errors.author }"
                        placeholder="请输入作者姓名"
                        required
                      >
                      <div v-if="errors.author" class="invalid-feedback">
                        {{ errors.author }}
                      </div>
                    </div>
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label for="category" class="form-label">分类 *</label>
                      <select
                        class="form-select"
                        id="category"
                        v-model="form.category"
                        :class="{ 'is-invalid': errors.category }"
                        required
                      >
                        <option value="">请选择分类</option>
                        <option v-for="category in booksStore.categories" :key="category.id" :value="category.name">
                          {{ category.name }}
                        </option>
                      </select>
                      <div v-if="errors.category" class="invalid-feedback">
                        {{ errors.category }}
                      </div>
                    </div>
                  </div>
                  
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label for="condition" class="form-label">成色 *</label>
                      <select
                        class="form-select"
                        id="condition"
                        v-model="form.condition"
                        :class="{ 'is-invalid': errors.condition }"
                        required
                      >
                        <option value="">请选择成色</option>
                        <option value="全新">全新</option>
                        <option value="九成新">九成新</option>
                        <option value="八成新">八成新</option>
                        <option value="七成新">七成新</option>
                        <option value="六成新">六成新</option>
                      </select>
                      <div v-if="errors.condition" class="invalid-feedback">
                        {{ errors.condition }}
                      </div>
                    </div>
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label for="originalPrice" class="form-label">原价 *</label>
                      <div class="input-group">
                        <span class="input-group-text">¥</span>
                        <input
                          type="number"
                          class="form-control"
                          id="originalPrice"
                          v-model.number="form.originalPrice"
                          :class="{ 'is-invalid': errors.originalPrice }"
                          placeholder="0.00"
                          min="0"
                          step="0.01"
                          required
                        >
                        <div v-if="errors.originalPrice" class="invalid-feedback">
                          {{ errors.originalPrice }}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label for="price" class="form-label">售价 *</label>
                      <div class="input-group">
                        <span class="input-group-text">¥</span>
                        <input
                          type="number"
                          class="form-control"
                          id="price"
                          v-model.number="form.price"
                          :class="{ 'is-invalid': errors.price }"
                          placeholder="0.00"
                          min="0"
                          step="0.01"
                          required
                        >
                        <div v-if="errors.price" class="invalid-feedback">
                          {{ errors.price }}
                        </div>
                      </div>
                      <div v-if="form.originalPrice && form.price" class="form-text">
                        <span v-if="form.price < form.originalPrice" class="text-success">
                          节省 ¥{{ (form.originalPrice - form.price).toFixed(2) }} 
                          ({{ Math.round((1 - form.price / form.originalPrice) * 100) }}% off)
                        </span>
                        <span v-else-if="form.price > form.originalPrice" class="text-warning">
                          售价不应高于原价
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="mb-3">
                  <label for="cover" class="form-label">封面图片链接</label>
                  <input
                    type="url"
                    class="form-control"
                    id="cover"
                    v-model="form.cover"
                    :class="{ 'is-invalid': errors.cover }"
                    placeholder="请输入图片链接（可选）"
                  >
                  <div class="form-text">
                    如不填写，系统将使用默认封面
                  </div>
                  <div v-if="errors.cover" class="invalid-feedback">
                    {{ errors.cover }}
                  </div>
                </div>

                <div class="mb-3">
                  <label for="description" class="form-label">图书描述 *</label>
                  <textarea
                    class="form-control"
                    id="description"
                    v-model="form.description"
                    :class="{ 'is-invalid': errors.description }"
                    rows="4"
                    placeholder="请详细描述图书的状况、使用情况等..."
                    required
                  ></textarea>
                  <div class="form-text">
                    {{ form.description.length }}/500 字符
                  </div>
                  <div v-if="errors.description" class="invalid-feedback">
                    {{ errors.description }}
                  </div>
                </div>

                <div v-if="errors.general" class="alert alert-danger">
                  {{ errors.general }}
                </div>

                <div class="d-flex justify-content-between">
                  <button type="button" class="btn btn-secondary" @click="handleReset">
                    <i class="bi bi-arrow-clockwise me-1"></i>
                    重置
                  </button>
                  <button
                    type="submit"
                    class="btn btn-primary"
                    :disabled="loading"
                  >
                    <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
                    <i v-else class="bi bi-check-lg me-1"></i>
                    {{ loading ? '发布中...' : '发布图书' }}
                  </button>
                </div>
              </form>
            </div>
          </div>

          <!-- 发布须知 -->
          <div class="card mt-4">
            <div class="card-header">
              <h6 class="mb-0">
                <i class="bi bi-info-circle me-2"></i>
                发布须知
              </h6>
            </div>
            <div class="card-body">
              <ul class="mb-0 small text-muted">
                <li>请确保图书信息真实准确，虚假信息将被下架</li>
                <li>图书售价建议不超过原价的80%</li>
                <li>请详细描述图书状况，包括是否有笔记、破损等</li>
                <li>每本图书默认库存为1，售出后自动下架</li>
                <li>发布后可在"我的发布"中管理图书信息</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useBooksStore } from '@/stores/books'

export default {
  name: 'PublishBook',
  setup() {
    const router = useRouter()
    const userStore = useUserStore()
    const booksStore = useBooksStore()
    
    const loading = ref(false)
    const form = reactive({
      title: '',
      author: '',
      category: '',
      condition: '',
      originalPrice: '',
      price: '',
      cover: '',
      description: ''
    })
    const errors = reactive({})

    const validateForm = () => {
      const newErrors = {}
      
      if (!form.title.trim()) {
        newErrors.title = '请输入图书名称'
      }
      
      if (!form.author.trim()) {
        newErrors.author = '请输入作者姓名'
      }
      
      if (!form.category) {
        newErrors.category = '请选择分类'
      }
      
      if (!form.condition) {
        newErrors.condition = '请选择成色'
      }
      
      if (!form.originalPrice || form.originalPrice <= 0) {
        newErrors.originalPrice = '请输入有效的原价'
      }
      
      if (!form.price || form.price <= 0) {
        newErrors.price = '请输入有效的售价'
      } else if (form.originalPrice && form.price > form.originalPrice) {
        newErrors.price = '售价不应高于原价'
      }
      
      if (!form.description.trim()) {
        newErrors.description = '请输入图书描述'
      } else if (form.description.length > 500) {
        newErrors.description = '描述不能超过500字符'
      }
      
      if (form.cover && !isValidUrl(form.cover)) {
        newErrors.cover = '请输入有效的图片链接'
      }
      
      Object.assign(errors, newErrors)
      return Object.keys(newErrors).length === 0
    }

    const isValidUrl = (string) => {
      try {
        new URL(string)
        return true
      } catch (_) {
        return false
      }
    }

    const handleSubmit = async () => {
      // 清除之前的错误
      Object.keys(errors).forEach(key => delete errors[key])
      
      if (!validateForm()) {
        return
      }

      loading.value = true
      
      try {
        const bookData = {
          ...form,
          sellerId: userStore.user.id,
          sellerName: userStore.user.username,
          cover: form.cover || `https://via.placeholder.com/200x280?text=${encodeURIComponent(form.title)}`
        }
        
        await booksStore.createBook(bookData)
        
        // 发布成功，跳转到我的发布页面
        router.push('/my-books')
      } catch (error) {
        errors.general = error.message || '发布失败，请重试'
      } finally {
        loading.value = false
      }
    }

    const handleReset = () => {
      Object.keys(form).forEach(key => {
        form[key] = ''
      })
      Object.keys(errors).forEach(key => delete errors[key])
    }

    onMounted(async () => {
      userStore.initUser()
      
      // 加载分类列表
      try {
        await booksStore.fetchCategories()
      } catch (error) {
        console.error('加载分类失败:', error)
      }
    })

    return {
      userStore,
      booksStore,
      form,
      errors,
      loading,
      handleSubmit,
      handleReset
    }
  }
}
</script>

<style scoped>
.card {
  border: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-control:focus,
.form-select:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}
</style>
