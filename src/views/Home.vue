<template>
  <div>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
      <div class="container">
        <router-link class="navbar-brand" to="/">
          <i class="bi bi-book me-2"></i>
          校园二手书交易系统
        </router-link>
        
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
          <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav me-auto">
            <li class="nav-item">
              <router-link class="nav-link" to="/">首页</router-link>
            </li>
            <li class="nav-item" v-if="userStore.isLoggedIn">
              <router-link class="nav-link" to="/publish">发布图书</router-link>
            </li>
          </ul>
          
          <ul class="navbar-nav">
            <template v-if="userStore.isLoggedIn">
              <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                  <img :src="userStore.user.avatar" alt="头像" class="rounded-circle me-2" width="24" height="24">
                  {{ userStore.user.username }}
                </a>
                <ul class="dropdown-menu">
                  <li><router-link class="dropdown-item" to="/orders">我的订单</router-link></li>
                  <li><router-link class="dropdown-item" to="/my-books">我的发布</router-link></li>
                  <li><router-link class="dropdown-item" to="/balance">我的余额</router-link></li>
                  <li v-if="userStore.isAdmin"><hr class="dropdown-divider"></li>
                  <li v-if="userStore.isAdmin">
                    <router-link class="dropdown-item" to="/admin/recharge">用户充值</router-link>
                  </li>
                  <li><hr class="dropdown-divider"></li>
                  <li><a class="dropdown-item" href="#" @click="handleLogout">退出登录</a></li>
                </ul>
              </li>
            </template>
            <template v-else>
              <li class="nav-item">
                <router-link class="nav-link" to="/login">登录</router-link>
              </li>
              <li class="nav-item">
                <router-link class="nav-link" to="/register">注册</router-link>
              </li>
            </template>
          </ul>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container mt-4">
      <!-- 搜索和筛选 -->
      <div class="row mb-4">
        <div class="col-md-8">
          <div class="input-group">
            <input
              type="text"
              class="form-control"
              placeholder="搜索图书名称、作者..."
              v-model="searchKeyword"
              @keyup.enter="handleSearch"
            >
            <button class="btn btn-outline-primary" @click="handleSearch">
              <i class="bi bi-search"></i> 搜索
            </button>
          </div>
        </div>
        <div class="col-md-4">
          <select class="form-select" v-model="selectedCategory" @change="handleCategoryChange">
            <option value="all">所有分类</option>
            <option v-for="category in booksStore.categories" :key="category.id" :value="category.name">
              {{ category.name }} ({{ category.count }})
            </option>
          </select>
        </div>
      </div>

      <!-- 排序选项 -->
      <div class="row mb-3">
        <div class="col-md-6">
          <small class="text-muted">共找到 {{ booksStore.total }} 本图书</small>
        </div>
        <div class="col-md-6 text-end">
          <div class="btn-group btn-group-sm">
            <button
              type="button"
              class="btn"
              :class="sortBy === 'time_desc' ? 'btn-primary' : 'btn-outline-primary'"
              @click="handleSort('time_desc')"
            >
              最新发布
            </button>
            <button
              type="button"
              class="btn"
              :class="sortBy === 'price_asc' ? 'btn-primary' : 'btn-outline-primary'"
              @click="handleSort('price_asc')"
            >
              价格从低到高
            </button>
            <button
              type="button"
              class="btn"
              :class="sortBy === 'price_desc' ? 'btn-primary' : 'btn-outline-primary'"
              @click="handleSort('price_desc')"
            >
              价格从高到低
            </button>
            <button
              type="button"
              class="btn"
              :class="sortBy === 'views_desc' ? 'btn-primary' : 'btn-outline-primary'"
              @click="handleSort('views_desc')"
            >
              最多浏览
            </button>
          </div>
        </div>
      </div>

      <!-- 图书列表 -->
      <div v-if="booksStore.loading" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">加载中...</span>
        </div>
      </div>

      <div v-else-if="booksStore.books.length === 0" class="text-center py-5">
        <i class="bi bi-book display-1 text-muted"></i>
        <h4 class="text-muted mt-3">暂无图书</h4>
        <p class="text-muted">试试调整搜索条件或
          <router-link to="/publish" v-if="userStore.isLoggedIn">发布第一本图书</router-link>
          <router-link to="/login" v-else>登录后发布图书</router-link>
        </p>
      </div>

      <div v-else class="row">
        <div class="col-lg-3 col-md-4 col-sm-6 mb-4" v-for="book in booksStore.books" :key="book.id">
          <div class="card h-100 book-card" @click="goToBookDetail(book.id)">
            <div class="position-relative">
              <img :src="book.cover" class="card-img-top" alt="图书封面">
              <span
                class="badge position-absolute top-0 end-0 m-2"
                :class="book.status === 'available' ? 'bg-success' : 'bg-secondary'"
              >
                {{ book.status === 'available' ? '在售' : '已售' }}
              </span>
            </div>
            <div class="card-body d-flex flex-column">
              <h6 class="card-title text-truncate" :title="book.title">{{ book.title }}</h6>
              <p class="card-text text-muted small mb-2">{{ book.author }}</p>
              <p class="card-text text-truncate small" :title="book.description">{{ book.description }}</p>
              <div class="mt-auto">
                <div class="d-flex justify-content-between align-items-center">
                  <div>
                    <span class="text-danger fw-bold">¥{{ book.price }}</span>
                    <small class="text-muted text-decoration-line-through ms-2">¥{{ book.originalPrice }}</small>
                  </div>
                  <small class="text-muted">{{ book.condition }}</small>
                </div>
                <div class="d-flex justify-content-between align-items-center mt-2">
                  <small class="text-muted">{{ book.sellerName }}</small>
                  <small class="text-muted">
                    <i class="bi bi-eye me-1"></i>{{ book.views }}
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <nav v-if="booksStore.total > booksStore.pageSize" class="mt-4">
        <ul class="pagination justify-content-center">
          <li class="page-item" :class="{ disabled: booksStore.currentPage === 1 }">
            <a class="page-link" href="#" @click.prevent="changePage(booksStore.currentPage - 1)">上一页</a>
          </li>
          <li
            class="page-item"
            v-for="page in totalPages"
            :key="page"
            :class="{ active: page === booksStore.currentPage }"
          >
            <a class="page-link" href="#" @click.prevent="changePage(page)">{{ page }}</a>
          </li>
          <li class="page-item" :class="{ disabled: booksStore.currentPage === totalPages }">
            <a class="page-link" href="#" @click.prevent="changePage(booksStore.currentPage + 1)">下一页</a>
          </li>
        </ul>
      </nav>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useBooksStore } from '@/stores/books'

export default {
  name: 'Home',
  setup() {
    const router = useRouter()
    const userStore = useUserStore()
    const booksStore = useBooksStore()
    
    const searchKeyword = ref('')
    const selectedCategory = ref('all')
    const sortBy = ref('time_desc')

    const totalPages = computed(() => {
      return Math.ceil(booksStore.total / booksStore.pageSize)
    })

    const handleSearch = () => {
      loadBooks()
    }

    const handleCategoryChange = () => {
      loadBooks()
    }

    const handleSort = (sort) => {
      sortBy.value = sort
      loadBooks()
    }

    const loadBooks = async () => {
      const params = {
        keyword: searchKeyword.value,
        category: selectedCategory.value,
        sortBy: sortBy.value,
        status: 'available'
      }
      
      try {
        await booksStore.fetchBooks(params)
      } catch (error) {
        console.error('加载图书失败:', error)
      }
    }

    const changePage = (page) => {
      if (page < 1 || page > totalPages.value) return
      booksStore.setPage(page)
      loadBooks()
    }

    const goToBookDetail = (id) => {
      router.push(`/book/${id}`)
    }

    const handleLogout = () => {
      userStore.logout()
      router.push('/login')
    }

    onMounted(async () => {
      // 初始化用户信息
      userStore.initUser()
      
      // 加载分类和图书
      try {
        await booksStore.fetchCategories()
        await loadBooks()
      } catch (error) {
        console.error('初始化失败:', error)
      }
    })

    return {
      userStore,
      booksStore,
      searchKeyword,
      selectedCategory,
      sortBy,
      totalPages,
      handleSearch,
      handleCategoryChange,
      handleSort,
      changePage,
      goToBookDetail,
      handleLogout
    }
  }
}
</script>

<style scoped>
.book-card {
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.book-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.card-img-top {
  height: 200px;
  object-fit: cover;
}

.navbar-brand {
  font-weight: bold;
}
</style>
