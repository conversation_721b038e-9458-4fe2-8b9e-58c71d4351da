<template>
  <div class="container-fluid vh-100 d-flex align-items-center justify-content-center bg-light">
    <div class="row w-100">
      <div class="col-md-6 col-lg-4 mx-auto">
        <div class="card shadow">
          <div class="card-body p-5">
            <div class="text-center mb-4">
              <h2 class="card-title text-primary">
                <i class="bi bi-book me-2"></i>
                校园二手书交易系统
              </h2>
              <p class="text-muted">创建新账户</p>
            </div>

            <form @submit.prevent="handleRegister">
              <div class="mb-3">
                <label for="username" class="form-label">用户名</label>
                <input
                  type="text"
                  class="form-control"
                  id="username"
                  v-model="form.username"
                  :class="{ 'is-invalid': errors.username }"
                  placeholder="请输入用户名"
                  required
                >
                <div v-if="errors.username" class="invalid-feedback">
                  {{ errors.username }}
                </div>
              </div>

              <div class="mb-3">
                <label for="email" class="form-label">邮箱</label>
                <input
                  type="email"
                  class="form-control"
                  id="email"
                  v-model="form.email"
                  :class="{ 'is-invalid': errors.email }"
                  placeholder="请输入邮箱地址"
                  required
                >
                <div v-if="errors.email" class="invalid-feedback">
                  {{ errors.email }}
                </div>
              </div>

              <div class="mb-3">
                <label for="password" class="form-label">密码</label>
                <input
                  type="password"
                  class="form-control"
                  id="password"
                  v-model="form.password"
                  :class="{ 'is-invalid': errors.password }"
                  placeholder="请输入密码（至少6位）"
                  required
                >
                <div v-if="errors.password" class="invalid-feedback">
                  {{ errors.password }}
                </div>
              </div>

              <div class="mb-3">
                <label for="confirmPassword" class="form-label">确认密码</label>
                <input
                  type="password"
                  class="form-control"
                  id="confirmPassword"
                  v-model="form.confirmPassword"
                  :class="{ 'is-invalid': errors.confirmPassword }"
                  placeholder="请再次输入密码"
                  required
                >
                <div v-if="errors.confirmPassword" class="invalid-feedback">
                  {{ errors.confirmPassword }}
                </div>
              </div>

              <div class="mb-3 form-check">
                <input
                  type="checkbox"
                  class="form-check-input"
                  id="agree"
                  v-model="form.agree"
                  :class="{ 'is-invalid': errors.agree }"
                  required
                >
                <label class="form-check-label" for="agree">
                  我同意<a href="#" class="text-primary">用户协议</a>和<a href="#" class="text-primary">隐私政策</a>
                </label>
                <div v-if="errors.agree" class="invalid-feedback">
                  {{ errors.agree }}
                </div>
              </div>

              <div v-if="errors.general" class="alert alert-danger">
                {{ errors.general }}
              </div>

              <button
                type="submit"
                class="btn btn-primary w-100 mb-3"
                :disabled="loading"
              >
                <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
                {{ loading ? '注册中...' : '注册' }}
              </button>
            </form>

            <div class="text-center">
              <p class="mb-0">
                已有账户？
                <router-link to="/login" class="text-primary text-decoration-none">
                  立即登录
                </router-link>
              </p>
            </div>

            <!-- 注册说明 -->
            <div class="mt-4 p-3 bg-success bg-opacity-10 rounded">
              <h6 class="text-success mb-2">注册说明</h6>
              <small class="text-muted">
                <div>• 新用户注册成功后将获得100元余额</div>
                <div>• 请使用真实邮箱地址注册</div>
                <div>• 用户名一旦注册不可修改</div>
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

export default {
  name: 'Register',
  setup() {
    const router = useRouter()
    const userStore = useUserStore()
    
    const loading = ref(false)
    const form = reactive({
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      agree: false
    })
    const errors = reactive({})

    const validateForm = () => {
      const newErrors = {}
      
      if (!form.username.trim()) {
        newErrors.username = '请输入用户名'
      } else if (form.username.length < 3) {
        newErrors.username = '用户名至少3位'
      } else if (!/^[a-zA-Z0-9_]+$/.test(form.username)) {
        newErrors.username = '用户名只能包含字母、数字和下划线'
      }
      
      if (!form.email.trim()) {
        newErrors.email = '请输入邮箱'
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
        newErrors.email = '请输入有效的邮箱地址'
      }
      
      if (!form.password.trim()) {
        newErrors.password = '请输入密码'
      } else if (form.password.length < 6) {
        newErrors.password = '密码至少6位'
      }
      
      if (!form.confirmPassword.trim()) {
        newErrors.confirmPassword = '请确认密码'
      } else if (form.password !== form.confirmPassword) {
        newErrors.confirmPassword = '两次输入的密码不一致'
      }
      
      if (!form.agree) {
        newErrors.agree = '请同意用户协议和隐私政策'
      }
      
      Object.assign(errors, newErrors)
      return Object.keys(newErrors).length === 0
    }

    const handleRegister = async () => {
      // 清除之前的错误
      Object.keys(errors).forEach(key => delete errors[key])
      
      if (!validateForm()) {
        return
      }

      loading.value = true
      
      try {
        await userStore.registerUser({
          username: form.username,
          email: form.email,
          password: form.password
        })
        
        // 注册成功，跳转到首页
        router.push('/')
      } catch (error) {
        errors.general = error.message || '注册失败，请重试'
      } finally {
        loading.value = false
      }
    }

    return {
      form,
      errors,
      loading,
      handleRegister
    }
  }
}
</script>

<style scoped>
.card {
  border: none;
  border-radius: 15px;
}

.btn-primary {
  border-radius: 10px;
}

.form-control {
  border-radius: 8px;
}
</style>
