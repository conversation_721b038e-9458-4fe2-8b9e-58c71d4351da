<template>
  <div class="container-fluid vh-100 d-flex align-items-center justify-content-center bg-light">
    <div class="row w-100">
      <div class="col-md-6 col-lg-4 mx-auto">
        <div class="card shadow">
          <div class="card-body p-5">
            <div class="text-center mb-4">
              <h2 class="card-title text-primary">
                <i class="bi bi-book me-2"></i>
                校园二手书交易系统
              </h2>
              <p class="text-muted">登录您的账户</p>
            </div>

            <form @submit.prevent="handleLogin">
              <div class="mb-3">
                <label for="username" class="form-label">用户名</label>
                <input
                  type="text"
                  class="form-control"
                  id="username"
                  v-model="form.username"
                  :class="{ 'is-invalid': errors.username }"
                  placeholder="请输入用户名"
                  required
                >
                <div v-if="errors.username" class="invalid-feedback">
                  {{ errors.username }}
                </div>
              </div>

              <div class="mb-3">
                <label for="password" class="form-label">密码</label>
                <input
                  type="password"
                  class="form-control"
                  id="password"
                  v-model="form.password"
                  :class="{ 'is-invalid': errors.password }"
                  placeholder="请输入密码"
                  required
                >
                <div v-if="errors.password" class="invalid-feedback">
                  {{ errors.password }}
                </div>
              </div>

              <div class="mb-3 form-check">
                <input
                  type="checkbox"
                  class="form-check-input"
                  id="remember"
                  v-model="form.remember"
                >
                <label class="form-check-label" for="remember">
                  记住我
                </label>
              </div>

              <button
                type="submit"
                class="btn btn-primary w-100 mb-3"
                :disabled="loading"
              >
                <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
                {{ loading ? '登录中...' : '登录' }}
              </button>
            </form>

            <div class="text-center">
              <p class="mb-0">
                还没有账户？
                <router-link to="/register" class="text-primary text-decoration-none">
                  立即注册
                </router-link>
              </p>
            </div>

            <!-- 测试账户提示 -->
            <div class="mt-4 p-3 bg-info bg-opacity-10 rounded">
              <h6 class="text-info mb-2">测试账户</h6>
              <small class="text-muted">
                <div>管理员：admin / 123456</div>
                <div>普通用户：student1 / 123456</div>
                <div>普通用户：student2 / 123456</div>
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

export default {
  name: 'Login',
  setup() {
    const router = useRouter()
    const userStore = useUserStore()
    
    const loading = ref(false)
    const form = reactive({
      username: '',
      password: '',
      remember: false
    })
    const errors = reactive({})

    const validateForm = () => {
      const newErrors = {}
      
      if (!form.username.trim()) {
        newErrors.username = '请输入用户名'
      }
      
      if (!form.password.trim()) {
        newErrors.password = '请输入密码'
      } else if (form.password.length < 6) {
        newErrors.password = '密码至少6位'
      }
      
      Object.assign(errors, newErrors)
      return Object.keys(newErrors).length === 0
    }

    const handleLogin = async () => {
      // 清除之前的错误
      Object.keys(errors).forEach(key => delete errors[key])
      
      if (!validateForm()) {
        return
      }

      loading.value = true
      
      try {
        await userStore.loginUser({
          username: form.username,
          password: form.password
        })
        
        // 登录成功，跳转到首页
        router.push('/')
      } catch (error) {
        errors.general = error.message || '登录失败，请重试'
      } finally {
        loading.value = false
      }
    }

    return {
      form,
      errors,
      loading,
      handleLogin
    }
  }
}
</script>

<style scoped>
.card {
  border: none;
  border-radius: 15px;
}

.btn-primary {
  border-radius: 10px;
}

.form-control {
  border-radius: 8px;
}
</style>
