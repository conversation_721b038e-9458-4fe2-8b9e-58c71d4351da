<template>
  <div class="container mt-4">
    <h2>调试页面</h2>
    
    <div class="row">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h5>测试图书API</h5>
          </div>
          <div class="card-body">
            <button class="btn btn-primary" @click="testBooksAPI">测试获取图书</button>
            <button class="btn btn-secondary ms-2" @click="testCategoriesAPI">测试获取分类</button>
            
            <div v-if="booksResult" class="mt-3">
              <h6>图书结果:</h6>
              <pre>{{ JSON.stringify(booksResult, null, 2) }}</pre>
            </div>
            
            <div v-if="categoriesResult" class="mt-3">
              <h6>分类结果:</h6>
              <pre>{{ JSON.stringify(categoriesResult, null, 2) }}</pre>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h5>Store状态</h5>
          </div>
          <div class="card-body">
            <h6>Books Store:</h6>
            <p>Loading: {{ booksStore.loading }}</p>
            <p>Books Count: {{ booksStore.books.length }}</p>
            <p>Total: {{ booksStore.total }}</p>
            <p>Categories Count: {{ booksStore.categories.length }}</p>
            
            <h6>User Store:</h6>
            <p>Is Logged In: {{ userStore.isLoggedIn }}</p>
            <p>User: {{ userStore.user ? userStore.user.username : 'None' }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useUserStore } from '@/stores/user'
import { useBooksStore } from '@/stores/books'
import { getBooks, getCategories } from '@/api/books'

export default {
  name: 'Debug',
  setup() {
    const userStore = useUserStore()
    const booksStore = useBooksStore()
    const booksResult = ref(null)
    const categoriesResult = ref(null)

    const testBooksAPI = async () => {
      try {
        console.log('Testing books API...')
        const result = await getBooks({ status: 'available' })
        console.log('Books API result:', result)
        booksResult.value = result
      } catch (error) {
        console.error('Books API error:', error)
        booksResult.value = { error: error.message }
      }
    }

    const testCategoriesAPI = async () => {
      try {
        console.log('Testing categories API...')
        const result = await getCategories()
        console.log('Categories API result:', result)
        categoriesResult.value = result
      } catch (error) {
        console.error('Categories API error:', error)
        categoriesResult.value = { error: error.message }
      }
    }

    return {
      userStore,
      booksStore,
      booksResult,
      categoriesResult,
      testBooksAPI,
      testCategoriesAPI
    }
  }
}
</script>

<style scoped>
pre {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}
</style>
