<template>
  <div>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
      <div class="container">
        <router-link class="navbar-brand" to="/">
          <i class="bi bi-book me-2"></i>
          校园二手书交易系统
        </router-link>
        
        <div class="navbar-nav ms-auto">
          <router-link class="nav-link" to="/">
            <i class="bi bi-arrow-left me-1"></i>返回首页
          </router-link>
        </div>
      </div>
    </nav>

    <div class="container mt-4">
      <div class="row">
        <div class="col-md-8 mx-auto">
          <!-- 余额概览 -->
          <div class="card mb-4">
            <div class="card-body text-center">
              <div class="row">
                <div class="col-md-4">
                  <div class="balance-item">
                    <h2 class="text-primary mb-1">¥{{ userStore.user?.balance?.toFixed(2) || '0.00' }}</h2>
                    <p class="text-muted mb-0">当前余额</p>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="balance-item">
                    <h2 class="text-success mb-1">¥{{ totalEarnings.toFixed(2) }}</h2>
                    <p class="text-muted mb-0">总收益</p>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="balance-item">
                    <h2 class="text-info mb-1">¥{{ totalSpent.toFixed(2) }}</h2>
                    <p class="text-muted mb-0">总支出</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 快捷操作 -->
          <div class="card mb-4">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-lightning me-2"></i>
                快捷操作
              </h5>
            </div>
            <div class="card-body">
              <div class="row text-center">
                <div class="col-6 col-md-3 mb-3">
                  <button class="btn btn-outline-primary w-100" @click="showRechargeModal">
                    <i class="bi bi-plus-circle d-block mb-2" style="font-size: 2rem;"></i>
                    模拟充值
                  </button>
                </div>
                <div class="col-6 col-md-3 mb-3">
                  <router-link to="/publish" class="btn btn-outline-success w-100">
                    <i class="bi bi-journal-plus d-block mb-2" style="font-size: 2rem;"></i>
                    发布图书
                  </router-link>
                </div>
                <div class="col-6 col-md-3 mb-3">
                  <router-link to="/orders" class="btn btn-outline-info w-100">
                    <i class="bi bi-bag-check d-block mb-2" style="font-size: 2rem;"></i>
                    我的订单
                  </router-link>
                </div>
                <div class="col-6 col-md-3 mb-3">
                  <router-link to="/my-books" class="btn btn-outline-warning w-100">
                    <i class="bi bi-collection d-block mb-2" style="font-size: 2rem;"></i>
                    我的发布
                  </router-link>
                </div>
              </div>
            </div>
          </div>

          <!-- 交易记录 -->
          <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h5 class="mb-0">
                <i class="bi bi-clock-history me-2"></i>
                交易记录
              </h5>
              <div class="btn-group btn-group-sm">
                <button
                  type="button"
                  class="btn"
                  :class="recordType === 'all' ? 'btn-primary' : 'btn-outline-primary'"
                  @click="filterRecords('all')"
                >
                  全部
                </button>
                <button
                  type="button"
                  class="btn"
                  :class="recordType === 'income' ? 'btn-primary' : 'btn-outline-primary'"
                  @click="filterRecords('income')"
                >
                  收入
                </button>
                <button
                  type="button"
                  class="btn"
                  :class="recordType === 'expense' ? 'btn-primary' : 'btn-outline-primary'"
                  @click="filterRecords('expense')"
                >
                  支出
                </button>
              </div>
            </div>
            <div class="card-body">
              <div v-if="filteredRecords.length === 0" class="text-center py-4">
                <i class="bi bi-inbox display-4 text-muted"></i>
                <p class="text-muted mt-3">暂无交易记录</p>
              </div>
              
              <div v-else class="transaction-list">
                <div
                  v-for="record in filteredRecords"
                  :key="record.id"
                  class="transaction-item d-flex justify-content-between align-items-center py-3 border-bottom"
                >
                  <div class="d-flex align-items-center">
                    <div
                      class="transaction-icon me-3"
                      :class="record.type === 'income' ? 'bg-success' : 'bg-danger'"
                    >
                      <i
                        class="bi text-white"
                        :class="record.type === 'income' ? 'bi-arrow-down' : 'bi-arrow-up'"
                      ></i>
                    </div>
                    <div>
                      <h6 class="mb-1">{{ record.description }}</h6>
                      <small class="text-muted">{{ record.date }}</small>
                    </div>
                  </div>
                  <div class="text-end">
                    <span
                      class="fw-bold"
                      :class="record.type === 'income' ? 'text-success' : 'text-danger'"
                    >
                      {{ record.type === 'income' ? '+' : '-' }}¥{{ record.amount.toFixed(2) }}
                    </span>
                    <div class="small text-muted">
                      余额: ¥{{ record.balance.toFixed(2) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 充值模态框 -->
    <div class="modal fade" id="rechargeModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">模拟充值</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <p class="text-muted">选择充值金额（仅用于演示）</p>
            
            <div class="row mb-3">
              <div class="col-4" v-for="amount in quickAmounts" :key="amount">
                <button
                  class="btn btn-outline-primary w-100 mb-2"
                  @click="rechargeAmount = amount"
                  :class="{ 'active': rechargeAmount === amount }"
                >
                  ¥{{ amount }}
                </button>
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label">自定义金额</label>
              <div class="input-group">
                <span class="input-group-text">¥</span>
                <input
                  type="number"
                  class="form-control"
                  v-model.number="rechargeAmount"
                  min="1"
                  max="10000"
                  step="1"
                  placeholder="输入充值金额"
                >
              </div>
            </div>

            <div class="alert alert-info">
              <i class="bi bi-info-circle me-2"></i>
              <strong>当前余额：</strong>¥{{ userStore.user?.balance?.toFixed(2) || '0.00' }}
              <br>
              <strong>充值后余额：</strong>¥{{ ((userStore.user?.balance || 0) + (rechargeAmount || 0)).toFixed(2) }}
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button
              type="button"
              class="btn btn-primary"
              @click="handleRecharge"
              :disabled="!rechargeAmount || rechargeAmount <= 0 || recharging"
            >
              <span v-if="recharging" class="spinner-border spinner-border-sm me-2"></span>
              确认充值
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useOrdersStore } from '@/stores/orders'

export default {
  name: 'MyBalance',
  setup() {
    const userStore = useUserStore()
    const ordersStore = useOrdersStore()
    
    const recordType = ref('all')
    const rechargeAmount = ref(null)
    const recharging = ref(false)
    const quickAmounts = [10, 50, 100, 200, 500, 1000]
    
    // 模拟交易记录
    const transactionRecords = ref([
      {
        id: 1,
        type: 'income',
        description: '注册赠送',
        amount: 100,
        balance: 100,
        date: '2024-01-01'
      }
    ])

    const filteredRecords = computed(() => {
      if (recordType.value === 'all') return transactionRecords.value
      return transactionRecords.value.filter(record => record.type === recordType.value)
    })

    const totalEarnings = computed(() => {
      return transactionRecords.value
        .filter(record => record.type === 'income' && record.description !== '注册赠送' && record.description !== '充值')
        .reduce((total, record) => total + record.amount, 0)
    })

    const totalSpent = computed(() => {
      return transactionRecords.value
        .filter(record => record.type === 'expense')
        .reduce((total, record) => total + record.amount, 0)
    })

    const filterRecords = (type) => {
      recordType.value = type
    }

    const showRechargeModal = () => {
      rechargeAmount.value = null
      const modal = new bootstrap.Modal(document.getElementById('rechargeModal'))
      modal.show()
    }

    const handleRecharge = async () => {
      if (!rechargeAmount.value || rechargeAmount.value <= 0) return
      
      recharging.value = true
      
      try {
        // 模拟充值延迟
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // 更新用户余额
        const newBalance = (userStore.user.balance || 0) + rechargeAmount.value
        userStore.updateBalance(newBalance)
        
        // 添加交易记录
        const newRecord = {
          id: transactionRecords.value.length + 1,
          type: 'income',
          description: '充值',
          amount: rechargeAmount.value,
          balance: newBalance,
          date: new Date().toISOString().split('T')[0]
        }
        transactionRecords.value.unshift(newRecord)
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('rechargeModal'))
        modal.hide()
        
        alert(`充值成功！已充值 ¥${rechargeAmount.value}`)
      } catch (error) {
        alert('充值失败，请重试')
      } finally {
        recharging.value = false
      }
    }

    const loadTransactionHistory = async () => {
      // 这里可以加载真实的交易记录
      // 目前使用模拟数据
    }

    onMounted(async () => {
      userStore.initUser()
      await loadTransactionHistory()
    })

    return {
      userStore,
      recordType,
      rechargeAmount,
      recharging,
      quickAmounts,
      transactionRecords,
      filteredRecords,
      totalEarnings,
      totalSpent,
      filterRecords,
      showRechargeModal,
      handleRecharge
    }
  }
}
</script>

<style scoped>
.balance-item {
  padding: 1rem;
}

.transaction-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.transaction-item:last-child {
  border-bottom: none !important;
}

.btn.active {
  background-color: var(--bs-primary);
  border-color: var(--bs-primary);
  color: white;
}
</style>
