<template>
  <div>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
      <div class="container">
        <router-link class="navbar-brand" to="/">
          <i class="bi bi-book me-2"></i>
          校园二手书交易系统
        </router-link>
        
        <div class="navbar-nav ms-auto">
          <router-link class="nav-link" to="/publish">
            <i class="bi bi-plus-circle me-1"></i>发布图书
          </router-link>
          <router-link class="nav-link" to="/">
            <i class="bi bi-arrow-left me-1"></i>返回首页
          </router-link>
        </div>
      </div>
    </nav>

    <div class="container mt-4">
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h4 class="mb-0">
                <i class="bi bi-collection me-2"></i>
                我的发布
              </h4>
              <div class="btn-group btn-group-sm">
                <button
                  type="button"
                  class="btn"
                  :class="statusFilter === 'all' ? 'btn-primary' : 'btn-outline-primary'"
                  @click="filterByStatus('all')"
                >
                  全部 ({{ allBooks.length }})
                </button>
                <button
                  type="button"
                  class="btn"
                  :class="statusFilter === 'available' ? 'btn-primary' : 'btn-outline-primary'"
                  @click="filterByStatus('available')"
                >
                  在售 ({{ availableBooks.length }})
                </button>
                <button
                  type="button"
                  class="btn"
                  :class="statusFilter === 'sold' ? 'btn-primary' : 'btn-outline-primary'"
                  @click="filterByStatus('sold')"
                >
                  已售 ({{ soldBooks.length }})
                </button>
              </div>
            </div>
            <div class="card-body">
              <!-- 统计信息 -->
              <div class="row mb-4">
                <div class="col-md-3">
                  <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                      <h5 class="card-title">{{ allBooks.length }}</h5>
                      <p class="card-text">总发布数</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="card bg-success text-white">
                    <div class="card-body text-center">
                      <h5 class="card-title">{{ soldBooks.length }}</h5>
                      <p class="card-text">已售出</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="card bg-info text-white">
                    <div class="card-body text-center">
                      <h5 class="card-title">{{ availableBooks.length }}</h5>
                      <p class="card-text">在售中</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                      <h5 class="card-title">¥{{ totalEarnings.toFixed(2) }}</h5>
                      <p class="card-text">总收益</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 加载状态 -->
              <div v-if="booksStore.loading" class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">加载中...</span>
                </div>
              </div>

              <!-- 空状态 -->
              <div v-else-if="filteredBooks.length === 0" class="text-center py-5">
                <i class="bi bi-journal-plus display-1 text-muted"></i>
                <h5 class="text-muted mt-3">
                  {{ statusFilter === 'all' ? '还没有发布任何图书' : `暂无${getStatusText(statusFilter)}图书` }}
                </h5>
                <p class="text-muted">发布您的第一本二手图书，开始赚取收益吧！</p>
                <router-link to="/publish" class="btn btn-primary">
                  <i class="bi bi-plus-circle me-1"></i>
                  发布图书
                </router-link>
              </div>

              <!-- 图书列表 -->
              <div v-else class="row">
                <div class="col-lg-4 col-md-6 mb-4" v-for="book in filteredBooks" :key="book.id">
                  <div class="card book-card h-100">
                    <div class="position-relative">
                      <img :src="book.cover" class="card-img-top" alt="图书封面">
                      <span
                        class="badge position-absolute top-0 end-0 m-2"
                        :class="book.status === 'available' ? 'bg-success' : 'bg-secondary'"
                      >
                        {{ book.status === 'available' ? '在售' : '已售' }}
                      </span>
                    </div>
                    <div class="card-body d-flex flex-column">
                      <h6 class="card-title">{{ book.title }}</h6>
                      <p class="card-text text-muted small">{{ book.author }}</p>
                      <p class="card-text text-truncate small">{{ book.description }}</p>
                      
                      <div class="mt-auto">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                          <div>
                            <span class="text-danger fw-bold">¥{{ book.price }}</span>
                            <small class="text-muted text-decoration-line-through ms-2">¥{{ book.originalPrice }}</small>
                          </div>
                          <small class="text-muted">{{ book.condition }}</small>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mb-3">
                          <small class="text-muted">
                            <i class="bi bi-eye me-1"></i>{{ book.views }} 次浏览
                          </small>
                          <small class="text-muted">{{ book.publishedAt }}</small>
                        </div>

                        <div class="d-grid gap-2">
                          <div class="btn-group">
                            <button
                              class="btn btn-outline-primary btn-sm"
                              @click="viewBook(book.id)"
                            >
                              <i class="bi bi-eye me-1"></i>查看
                            </button>
                            <button
                              class="btn btn-outline-secondary btn-sm"
                              @click="editBook(book)"
                              :disabled="book.status === 'sold'"
                            >
                              <i class="bi bi-pencil me-1"></i>编辑
                            </button>
                            <button
                              class="btn btn-outline-danger btn-sm"
                              @click="deleteBook(book)"
                              :disabled="book.status === 'sold'"
                            >
                              <i class="bi bi-trash me-1"></i>删除
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑图书模态框 -->
    <div class="modal fade" id="editBookModal" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">编辑图书</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body" v-if="editingBook">
            <form @submit.prevent="handleUpdateBook">
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label">图书名称</label>
                    <input
                      type="text"
                      class="form-control"
                      v-model="editingBook.title"
                      required
                    >
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label">作者</label>
                    <input
                      type="text"
                      class="form-control"
                      v-model="editingBook.author"
                      required
                    >
                  </div>
                </div>
              </div>
              
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label">售价</label>
                    <div class="input-group">
                      <span class="input-group-text">¥</span>
                      <input
                        type="number"
                        class="form-control"
                        v-model.number="editingBook.price"
                        min="0"
                        step="0.01"
                        required
                      >
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label">成色</label>
                    <select class="form-select" v-model="editingBook.condition" required>
                      <option value="全新">全新</option>
                      <option value="九成新">九成新</option>
                      <option value="八成新">八成新</option>
                      <option value="七成新">七成新</option>
                      <option value="六成新">六成新</option>
                    </select>
                  </div>
                </div>
              </div>

              <div class="mb-3">
                <label class="form-label">图书描述</label>
                <textarea
                  class="form-control"
                  v-model="editingBook.description"
                  rows="3"
                  required
                ></textarea>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button
              type="button"
              class="btn btn-primary"
              @click="handleUpdateBook"
              :disabled="updating"
            >
              <span v-if="updating" class="spinner-border spinner-border-sm me-2"></span>
              保存修改
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteBookModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">确认删除</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body" v-if="deletingBook">
            <p>确定要删除图书《{{ deletingBook.title }}》吗？</p>
            <p class="text-danger small">此操作不可撤销！</p>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button
              type="button"
              class="btn btn-danger"
              @click="handleDeleteBook"
              :disabled="deleting"
            >
              <span v-if="deleting" class="spinner-border spinner-border-sm me-2"></span>
              确认删除
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useBooksStore } from '@/stores/books'

export default {
  name: 'MyBooks',
  setup() {
    const router = useRouter()
    const userStore = useUserStore()
    const booksStore = useBooksStore()
    
    const statusFilter = ref('all')
    const allBooks = ref([])
    const editingBook = ref(null)
    const deletingBook = ref(null)
    const updating = ref(false)
    const deleting = ref(false)

    const availableBooks = computed(() => 
      allBooks.value.filter(book => book.status === 'available')
    )

    const soldBooks = computed(() => 
      allBooks.value.filter(book => book.status === 'sold')
    )

    const filteredBooks = computed(() => {
      if (statusFilter.value === 'all') return allBooks.value
      return allBooks.value.filter(book => book.status === statusFilter.value)
    })

    const totalEarnings = computed(() => {
      return soldBooks.value.reduce((total, book) => total + book.price, 0)
    })

    const filterByStatus = (status) => {
      statusFilter.value = status
    }

    const getStatusText = (status) => {
      const statusMap = {
        available: '在售',
        sold: '已售',
        all: '全部'
      }
      return statusMap[status] || status
    }

    const loadMyBooks = async () => {
      if (!userStore.user) return
      
      try {
        const response = await booksStore.getUserBooks(userStore.user.id)
        allBooks.value = response.list
      } catch (error) {
        console.error('加载我的图书失败:', error)
      }
    }

    const viewBook = (id) => {
      router.push(`/book/${id}`)
    }

    const editBook = (book) => {
      editingBook.value = { ...book }
      const modal = new bootstrap.Modal(document.getElementById('editBookModal'))
      modal.show()
    }

    const handleUpdateBook = async () => {
      if (!editingBook.value) return
      
      updating.value = true
      
      try {
        await booksStore.editBook(editingBook.value.id, editingBook.value)
        
        // 更新本地数据
        const index = allBooks.value.findIndex(book => book.id === editingBook.value.id)
        if (index !== -1) {
          allBooks.value[index] = { ...editingBook.value }
        }
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('editBookModal'))
        modal.hide()
        
        alert('更新成功！')
      } catch (error) {
        alert(error.message || '更新失败，请重试')
      } finally {
        updating.value = false
      }
    }

    const deleteBook = (book) => {
      deletingBook.value = book
      const modal = new bootstrap.Modal(document.getElementById('deleteBookModal'))
      modal.show()
    }

    const handleDeleteBook = async () => {
      if (!deletingBook.value) return
      
      deleting.value = true
      
      try {
        await booksStore.removeBook(deletingBook.value.id)
        
        // 从本地数据中移除
        const index = allBooks.value.findIndex(book => book.id === deletingBook.value.id)
        if (index !== -1) {
          allBooks.value.splice(index, 1)
        }
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteBookModal'))
        modal.hide()
        
        alert('删除成功！')
      } catch (error) {
        alert(error.message || '删除失败，请重试')
      } finally {
        deleting.value = false
      }
    }

    onMounted(async () => {
      userStore.initUser()
      await loadMyBooks()
    })

    return {
      userStore,
      booksStore,
      statusFilter,
      allBooks,
      availableBooks,
      soldBooks,
      filteredBooks,
      totalEarnings,
      editingBook,
      deletingBook,
      updating,
      deleting,
      filterByStatus,
      getStatusText,
      viewBook,
      editBook,
      handleUpdateBook,
      deleteBook,
      handleDeleteBook
    }
  }
}
</script>

<style scoped>
.book-card {
  transition: transform 0.2s, box-shadow 0.2s;
}

.book-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.card-img-top {
  height: 200px;
  object-fit: cover;
}
</style>
