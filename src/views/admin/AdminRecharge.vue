<template>
  <div>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
      <div class="container">
        <router-link class="navbar-brand" to="/">
          <i class="bi bi-book me-2"></i>
          校园二手书交易系统
        </router-link>
        
        <div class="navbar-nav ms-auto">
          <span class="navbar-text me-3">
            <i class="bi bi-shield-check me-1"></i>
            管理员模式
          </span>
          <router-link class="nav-link" to="/">
            <i class="bi bi-arrow-left me-1"></i>返回首页
          </router-link>
        </div>
      </div>
    </nav>

    <div class="container mt-4">
      <div class="row">
        <div class="col-md-8 mx-auto">
          <div class="card">
            <div class="card-header">
              <h4 class="mb-0">
                <i class="bi bi-credit-card me-2"></i>
                用户充值管理
              </h4>
            </div>
            <div class="card-body">
              <!-- 用户搜索 -->
              <div class="mb-4">
                <label class="form-label">搜索用户</label>
                <div class="input-group">
                  <input
                    type="text"
                    class="form-control"
                    v-model="searchKeyword"
                    placeholder="输入用户名或邮箱搜索..."
                    @input="searchUsers"
                  >
                  <button class="btn btn-outline-primary" @click="searchUsers">
                    <i class="bi bi-search"></i>
                  </button>
                </div>
              </div>

              <!-- 用户列表 -->
              <div v-if="searchResults.length > 0" class="mb-4">
                <h6>搜索结果：</h6>
                <div class="list-group">
                  <div
                    v-for="user in searchResults"
                    :key="user.id"
                    class="list-group-item list-group-item-action"
                    :class="{ 'active': selectedUser?.id === user.id }"
                    @click="selectUser(user)"
                  >
                    <div class="d-flex justify-content-between align-items-center">
                      <div class="d-flex align-items-center">
                        <img :src="user.avatar" alt="头像" class="rounded-circle me-3" width="40" height="40">
                        <div>
                          <h6 class="mb-1">{{ user.username }}</h6>
                          <small class="text-muted">{{ user.email }}</small>
                        </div>
                      </div>
                      <div class="text-end">
                        <span class="badge bg-primary">¥{{ user.balance.toFixed(2) }}</span>
                        <div class="small text-muted">当前余额</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 充值表单 -->
              <div v-if="selectedUser" class="card bg-light">
                <div class="card-body">
                  <h6 class="card-title">
                    <i class="bi bi-person-check me-2"></i>
                    为 {{ selectedUser.username }} 充值
                  </h6>
                  
                  <div class="row mb-3">
                    <div class="col-md-6">
                      <div class="info-item">
                        <strong>用户名：</strong>{{ selectedUser.username }}
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="info-item">
                        <strong>当前余额：</strong>¥{{ selectedUser.balance.toFixed(2) }}
                      </div>
                    </div>
                  </div>

                  <form @submit.prevent="handleRecharge">
                    <div class="mb-3">
                      <label class="form-label">充值金额</label>
                      <div class="input-group">
                        <span class="input-group-text">¥</span>
                        <input
                          type="number"
                          class="form-control"
                          v-model.number="rechargeForm.amount"
                          :class="{ 'is-invalid': errors.amount }"
                          min="0.01"
                          step="0.01"
                          placeholder="请输入充值金额"
                          required
                        >
                        <div v-if="errors.amount" class="invalid-feedback">
                          {{ errors.amount }}
                        </div>
                      </div>
                    </div>

                    <div class="mb-3">
                      <label class="form-label">快捷金额</label>
                      <div class="btn-group d-flex" role="group">
                        <button
                          type="button"
                          class="btn btn-outline-primary"
                          v-for="amount in quickAmounts"
                          :key="amount"
                          @click="rechargeForm.amount = amount"
                        >
                          ¥{{ amount }}
                        </button>
                      </div>
                    </div>

                    <div class="mb-3">
                      <label class="form-label">充值原因</label>
                      <textarea
                        class="form-control"
                        v-model="rechargeForm.reason"
                        :class="{ 'is-invalid': errors.reason }"
                        rows="3"
                        placeholder="请输入充值原因（可选）"
                      ></textarea>
                      <div v-if="errors.reason" class="invalid-feedback">
                        {{ errors.reason }}
                      </div>
                    </div>

                    <div class="alert alert-info">
                      <i class="bi bi-info-circle me-2"></i>
                      <strong>充值后余额：</strong>
                      ¥{{ (selectedUser.balance + (rechargeForm.amount || 0)).toFixed(2) }}
                    </div>

                    <div v-if="errors.general" class="alert alert-danger">
                      {{ errors.general }}
                    </div>

                    <div class="d-flex justify-content-between">
                      <button
                        type="button"
                        class="btn btn-secondary"
                        @click="clearSelection"
                      >
                        <i class="bi bi-x-circle me-1"></i>
                        取消
                      </button>
                      <button
                        type="submit"
                        class="btn btn-primary"
                        :disabled="!rechargeForm.amount || rechargeForm.amount <= 0 || recharging"
                      >
                        <span v-if="recharging" class="spinner-border spinner-border-sm me-2"></span>
                        <i v-else class="bi bi-check-lg me-1"></i>
                        {{ recharging ? '充值中...' : '确认充值' }}
                      </button>
                    </div>
                  </form>
                </div>
              </div>

              <!-- 使用说明 -->
              <div v-else class="alert alert-info">
                <h6 class="alert-heading">
                  <i class="bi bi-lightbulb me-2"></i>
                  使用说明
                </h6>
                <ul class="mb-0">
                  <li>在搜索框中输入用户名或邮箱来查找用户</li>
                  <li>选择要充值的用户</li>
                  <li>输入充值金额和原因</li>
                  <li>确认充值后，用户余额将立即更新</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- 最近充值记录 -->
          <div class="card mt-4">
            <div class="card-header">
              <h6 class="mb-0">
                <i class="bi bi-clock-history me-2"></i>
                最近充值记录
              </h6>
            </div>
            <div class="card-body">
              <div v-if="rechargeHistory.length === 0" class="text-center py-3">
                <i class="bi bi-inbox text-muted"></i>
                <p class="text-muted mb-0">暂无充值记录</p>
              </div>
              
              <div v-else class="table-responsive">
                <table class="table table-sm">
                  <thead>
                    <tr>
                      <th>时间</th>
                      <th>用户</th>
                      <th>金额</th>
                      <th>原因</th>
                      <th>操作员</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="record in rechargeHistory" :key="record.id">
                      <td>{{ record.date }}</td>
                      <td>{{ record.username }}</td>
                      <td class="text-success fw-bold">+¥{{ record.amount.toFixed(2) }}</td>
                      <td>{{ record.reason || '-' }}</td>
                      <td>{{ record.operator }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { users } from '@/mock/data'

export default {
  name: 'AdminRecharge',
  setup() {
    const userStore = useUserStore()
    
    const searchKeyword = ref('')
    const searchResults = ref([])
    const selectedUser = ref(null)
    const recharging = ref(false)
    const quickAmounts = [10, 50, 100, 200, 500, 1000]
    
    const rechargeForm = reactive({
      amount: null,
      reason: ''
    })
    
    const errors = reactive({})
    
    const rechargeHistory = ref([])

    const searchUsers = () => {
      if (!searchKeyword.value.trim()) {
        searchResults.value = []
        return
      }
      
      const keyword = searchKeyword.value.toLowerCase()
      searchResults.value = users
        .filter(user => user.role !== 'admin') // 不显示管理员账户
        .filter(user => 
          user.username.toLowerCase().includes(keyword) ||
          user.email.toLowerCase().includes(keyword)
        )
        .slice(0, 5) // 最多显示5个结果
    }

    const selectUser = (user) => {
      selectedUser.value = user
      // 清空表单
      rechargeForm.amount = null
      rechargeForm.reason = ''
      Object.keys(errors).forEach(key => delete errors[key])
    }

    const clearSelection = () => {
      selectedUser.value = null
      rechargeForm.amount = null
      rechargeForm.reason = ''
      Object.keys(errors).forEach(key => delete errors[key])
    }

    const validateForm = () => {
      const newErrors = {}
      
      if (!rechargeForm.amount || rechargeForm.amount <= 0) {
        newErrors.amount = '请输入有效的充值金额'
      } else if (rechargeForm.amount > 10000) {
        newErrors.amount = '单次充值金额不能超过10000元'
      }
      
      if (rechargeForm.reason && rechargeForm.reason.length > 200) {
        newErrors.reason = '充值原因不能超过200字符'
      }
      
      Object.assign(errors, newErrors)
      return Object.keys(newErrors).length === 0
    }

    const handleRecharge = async () => {
      // 清除之前的错误
      Object.keys(errors).forEach(key => delete errors[key])
      
      if (!validateForm()) {
        return
      }

      recharging.value = true
      
      try {
        // 模拟充值延迟
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // 更新用户余额
        selectedUser.value.balance += rechargeForm.amount
        
        // 添加充值记录
        const newRecord = {
          id: rechargeHistory.value.length + 1,
          date: new Date().toLocaleString(),
          username: selectedUser.value.username,
          amount: rechargeForm.amount,
          reason: rechargeForm.reason,
          operator: userStore.user.username
        }
        rechargeHistory.value.unshift(newRecord)
        
        alert(`充值成功！已为 ${selectedUser.value.username} 充值 ¥${rechargeForm.amount}`)
        
        // 清空表单
        clearSelection()
      } catch (error) {
        errors.general = error.message || '充值失败，请重试'
      } finally {
        recharging.value = false
      }
    }

    onMounted(() => {
      userStore.initUser()
    })

    return {
      userStore,
      searchKeyword,
      searchResults,
      selectedUser,
      rechargeForm,
      errors,
      recharging,
      quickAmounts,
      rechargeHistory,
      searchUsers,
      selectUser,
      clearSelection,
      handleRecharge
    }
  }
}
</script>

<style scoped>
.info-item {
  padding: 0.5rem 0;
}

.list-group-item.active {
  background-color: var(--bs-primary);
  border-color: var(--bs-primary);
}

.card {
  border: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
