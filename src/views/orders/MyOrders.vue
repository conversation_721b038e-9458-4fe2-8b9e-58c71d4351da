<template>
  <div>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
      <div class="container">
        <router-link class="navbar-brand" to="/">
          <i class="bi bi-book me-2"></i>
          校园二手书交易系统
        </router-link>
        
        <div class="navbar-nav ms-auto">
          <router-link class="nav-link" to="/">
            <i class="bi bi-arrow-left me-1"></i>返回首页
          </router-link>
        </div>
      </div>
    </nav>

    <div class="container mt-4">
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h4 class="mb-0">
                <i class="bi bi-bag-check me-2"></i>
                我的订单
              </h4>
            </div>
            <div class="card-body">
              <!-- 订单类型切换 -->
              <div class="btn-group mb-4" role="group">
                <button
                  type="button"
                  class="btn"
                  :class="orderType === 'buy' ? 'btn-primary' : 'btn-outline-primary'"
                  @click="switchOrderType('buy')"
                >
                  <i class="bi bi-cart me-1"></i>
                  我购买的 ({{ buyOrders.length }})
                </button>
                <button
                  type="button"
                  class="btn"
                  :class="orderType === 'sell' ? 'btn-primary' : 'btn-outline-primary'"
                  @click="switchOrderType('sell')"
                >
                  <i class="bi bi-currency-dollar me-1"></i>
                  我出售的 ({{ sellOrders.length }})
                </button>
              </div>

              <!-- 加载状态 -->
              <div v-if="ordersStore.loading" class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">加载中...</span>
                </div>
              </div>

              <!-- 空状态 -->
              <div v-else-if="currentOrders.length === 0" class="text-center py-5">
                <i class="bi bi-inbox display-1 text-muted"></i>
                <h5 class="text-muted mt-3">暂无订单</h5>
                <p class="text-muted">
                  {{ orderType === 'buy' ? '去首页看看有什么好书吧' : '发布一些图书来赚取收益吧' }}
                </p>
                <router-link
                  :to="orderType === 'buy' ? '/' : '/publish'"
                  class="btn btn-primary"
                >
                  {{ orderType === 'buy' ? '去购买图书' : '发布图书' }}
                </router-link>
              </div>

              <!-- 订单列表 -->
              <div v-else class="row">
                <div class="col-md-6 col-lg-4 mb-4" v-for="order in currentOrders" :key="order.id">
                  <div class="card order-card h-100">
                    <div class="card-body">
                      <div class="d-flex align-items-start mb-3">
                        <img
                          :src="order.bookCover"
                          alt="图书封面"
                          class="order-book-cover me-3"
                        >
                        <div class="flex-grow-1">
                          <h6 class="card-title mb-1">{{ order.bookTitle }}</h6>
                          <p class="text-muted small mb-2">
                            订单号：{{ order.id }}
                          </p>
                          <div class="d-flex justify-content-between align-items-center">
                            <span class="text-danger fw-bold">¥{{ order.price }}</span>
                            <span
                              class="badge"
                              :class="getStatusBadgeClass(order.status)"
                            >
                              {{ getStatusText(order.status) }}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div class="order-details">
                        <div class="row text-center">
                          <div class="col-6">
                            <small class="text-muted d-block">
                              {{ orderType === 'buy' ? '卖家' : '买家' }}
                            </small>
                            <span class="fw-bold">
                              {{ orderType === 'buy' ? order.sellerName : order.buyerName }}
                            </span>
                          </div>
                          <div class="col-6">
                            <small class="text-muted d-block">交易时间</small>
                            <span>{{ order.createdAt }}</span>
                          </div>
                        </div>
                      </div>

                      <div class="mt-3 d-flex justify-content-between">
                        <button
                          class="btn btn-outline-primary btn-sm"
                          @click="viewOrderDetail(order)"
                        >
                          <i class="bi bi-eye me-1"></i>
                          查看详情
                        </button>
                        
                        <div v-if="orderType === 'buy'">
                          <button
                            class="btn btn-primary btn-sm"
                            @click="buyAgain(order.bookId)"
                            v-if="order.status === 'completed'"
                          >
                            <i class="bi bi-arrow-repeat me-1"></i>
                            再次购买
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 分页 -->
              <nav v-if="ordersStore.total > ordersStore.pageSize" class="mt-4">
                <ul class="pagination justify-content-center">
                  <li class="page-item" :class="{ disabled: ordersStore.currentPage === 1 }">
                    <a class="page-link" href="#" @click.prevent="changePage(ordersStore.currentPage - 1)">上一页</a>
                  </li>
                  <li
                    class="page-item"
                    v-for="page in totalPages"
                    :key="page"
                    :class="{ active: page === ordersStore.currentPage }"
                  >
                    <a class="page-link" href="#" @click.prevent="changePage(page)">{{ page }}</a>
                  </li>
                  <li class="page-item" :class="{ disabled: ordersStore.currentPage === totalPages }">
                    <a class="page-link" href="#" @click.prevent="changePage(ordersStore.currentPage + 1)">下一页</a>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 订单详情模态框 -->
    <div class="modal fade" id="orderDetailModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">订单详情</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body" v-if="selectedOrder">
            <div class="text-center mb-3">
              <img :src="selectedOrder.bookCover" alt="图书封面" class="img-thumbnail" style="max-width: 120px;">
            </div>
            <table class="table table-borderless">
              <tr>
                <td class="text-muted">订单号：</td>
                <td>{{ selectedOrder.id }}</td>
              </tr>
              <tr>
                <td class="text-muted">图书名称：</td>
                <td>{{ selectedOrder.bookTitle }}</td>
              </tr>
              <tr>
                <td class="text-muted">交易金额：</td>
                <td class="text-danger fw-bold">¥{{ selectedOrder.price }}</td>
              </tr>
              <tr>
                <td class="text-muted">买家：</td>
                <td>{{ selectedOrder.buyerName }}</td>
              </tr>
              <tr>
                <td class="text-muted">卖家：</td>
                <td>{{ selectedOrder.sellerName }}</td>
              </tr>
              <tr>
                <td class="text-muted">订单状态：</td>
                <td>
                  <span
                    class="badge"
                    :class="getStatusBadgeClass(selectedOrder.status)"
                  >
                    {{ getStatusText(selectedOrder.status) }}
                  </span>
                </td>
              </tr>
              <tr>
                <td class="text-muted">创建时间：</td>
                <td>{{ selectedOrder.createdAt }}</td>
              </tr>
              <tr v-if="selectedOrder.completedAt">
                <td class="text-muted">完成时间：</td>
                <td>{{ selectedOrder.completedAt }}</td>
              </tr>
            </table>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useOrdersStore } from '@/stores/orders'

export default {
  name: 'MyOrders',
  setup() {
    const router = useRouter()
    const userStore = useUserStore()
    const ordersStore = useOrdersStore()
    
    const orderType = ref('buy') // 'buy' 或 'sell'
    const buyOrders = ref([])
    const sellOrders = ref([])
    const selectedOrder = ref(null)

    const currentOrders = computed(() => {
      return orderType.value === 'buy' ? buyOrders.value : sellOrders.value
    })

    const totalPages = computed(() => {
      return Math.ceil(ordersStore.total / ordersStore.pageSize)
    })

    const switchOrderType = async (type) => {
      orderType.value = type
      await loadOrders()
    }

    const loadOrders = async () => {
      if (!userStore.user) return
      
      try {
        if (orderType.value === 'buy') {
          const response = await ordersStore.getBuyOrders(userStore.user.id)
          buyOrders.value = response.list
        } else {
          const response = await ordersStore.getSellOrders(userStore.user.id)
          sellOrders.value = response.list
        }
      } catch (error) {
        console.error('加载订单失败:', error)
      }
    }

    const getStatusText = (status) => {
      const statusMap = {
        pending: '待处理',
        completed: '已完成',
        cancelled: '已取消'
      }
      return statusMap[status] || status
    }

    const getStatusBadgeClass = (status) => {
      const classMap = {
        pending: 'bg-warning',
        completed: 'bg-success',
        cancelled: 'bg-secondary'
      }
      return classMap[status] || 'bg-secondary'
    }

    const viewOrderDetail = (order) => {
      selectedOrder.value = order
      const modal = new bootstrap.Modal(document.getElementById('orderDetailModal'))
      modal.show()
    }

    const buyAgain = (bookId) => {
      router.push(`/book/${bookId}`)
    }

    const changePage = (page) => {
      if (page < 1 || page > totalPages.value) return
      ordersStore.setPage(page)
      loadOrders()
    }

    onMounted(async () => {
      userStore.initUser()
      await loadOrders()
    })

    return {
      userStore,
      ordersStore,
      orderType,
      buyOrders,
      sellOrders,
      currentOrders,
      selectedOrder,
      totalPages,
      switchOrderType,
      getStatusText,
      getStatusBadgeClass,
      viewOrderDetail,
      buyAgain,
      changePage
    }
  }
}
</script>

<style scoped>
.order-card {
  transition: transform 0.2s, box-shadow 0.2s;
}

.order-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.order-book-cover {
  width: 60px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
}

.order-details {
  background-color: #f8f9fa;
  padding: 10px;
  border-radius: 6px;
  font-size: 0.9em;
}
</style>
