import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

// 路由组件
const Home = () => import('@/views/Home.vue')
const Login = () => import('@/views/auth/Login.vue')
const Register = () => import('@/views/auth/Register.vue')
const BookDetail = () => import('@/views/books/BookDetail.vue')
const PublishBook = () => import('@/views/books/PublishBook.vue')
const MyOrders = () => import('@/views/orders/MyOrders.vue')
const MyBooks = () => import('@/views/profile/MyBooks.vue')
const MyBalance = () => import('@/views/profile/MyBalance.vue')
const AdminRecharge = () => import('@/views/admin/AdminRecharge.vue')
const Debug = () => import('@/views/Debug.vue')

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: { title: '首页' }
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { title: '登录', guest: true }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: { title: '注册', guest: true }
  },
  {
    path: '/book/:id',
    name: 'BookDetail',
    component: BookDetail,
    meta: { title: '图书详情' }
  },
  {
    path: '/publish',
    name: 'PublishBook',
    component: PublishBook,
    meta: { title: '发布图书', requiresAuth: true }
  },
  {
    path: '/orders',
    name: 'MyOrders',
    component: MyOrders,
    meta: { title: '我的订单', requiresAuth: true }
  },
  {
    path: '/my-books',
    name: 'MyBooks',
    component: MyBooks,
    meta: { title: '我的发布', requiresAuth: true }
  },
  {
    path: '/balance',
    name: 'MyBalance',
    component: MyBalance,
    meta: { title: '我的余额', requiresAuth: true }
  },
  {
    path: '/admin/recharge',
    name: 'AdminRecharge',
    component: AdminRecharge,
    meta: { title: '用户充值', requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/debug',
    name: 'Debug',
    component: Debug,
    meta: { title: '调试页面' }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 校园二手书交易系统` : '校园二手书交易系统'
  
  // 检查是否需要登录
  if (to.meta.requiresAuth && !userStore.isLoggedIn) {
    next('/login')
    return
  }
  
  // 检查是否需要管理员权限
  if (to.meta.requiresAdmin && !userStore.isAdmin) {
    next('/')
    return
  }
  
  // 已登录用户访问登录/注册页面，重定向到首页
  if (to.meta.guest && userStore.isLoggedIn) {
    next('/')
    return
  }
  
  next()
})

export default router
